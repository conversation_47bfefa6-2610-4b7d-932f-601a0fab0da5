/**
 * Test Authenticated Trip Creation
 * Tests trip creation with proper authentication to bypass RLS policies
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with correct credentials
const supabaseUrl = 'https://pcacyfyhxvzbjcouxzub.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjYWN5ZnloeHZ6Ympjb3V4enViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDE5MDgsImV4cCI6MjA2NzY3NzkwOH0.KXW5FFxjBo5crP_w5sXVK7xeH-fnhePo3FUs3MEIhZk';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuthenticatedTripCreation() {
  console.log('🧪 Testing Authenticated Trip Creation...\n');

  try {
    // Step 1: Find a passenger user to authenticate as
    console.log('1. Finding passenger user...');
    const { data: passengers, error: passengerError } = await supabase
      .from('profiles')
      .select('id, full_name, email, user_type')
      .eq('user_type', 'passenger')
      .limit(1);

    if (passengerError || !passengers || passengers.length === 0) {
      console.error('❌ No passenger profiles found:', passengerError);
      return;
    }

    const passenger = passengers[0];
    console.log(`✅ Found passenger: ${passenger.full_name} (${passenger.email})`);

    // Step 2: Check if we can get auth user info
    console.log('\n2. Checking authentication...');
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('⚠️ No authenticated user found. Testing with passenger_id directly...');
      
      // Step 3: Find a driver to assign to the trip
      console.log('\n3. Finding available driver...');
      const { data: drivers, error: driverError } = await supabase
        .from('drivers')
        .select('id, user_id, is_online, verification_status')
        .eq('is_online', true)
        .eq('verification_status', 'approved')
        .limit(1);

      if (driverError || !drivers || drivers.length === 0) {
        console.log('⚠️ No online approved drivers found. Creating trip without driver assignment...');
        var driverId = null;
      } else {
        var driverId = drivers[0].id;
        console.log(`✅ Found driver: ${driverId}`);
      }

      // Step 4: Test trip creation with driver assignment
      console.log('\n4. Testing trip creation with driver assignment...');

      const testTripData = {
        passenger_id: passenger.id,
        driver_id: driverId, // Assign driver if available
        pickup_location: 'Test Pickup Location, Johannesburg',
        pickup_coordinates: '(28.0473,-26.2041)',
        destination_location: 'Test Destination, Sandton',
        destination_coordinates: '(28.0436,-26.1076)',
        ride_type: 'SheRide',
        status: 'requested',
        fare_amount: 45.50,
        distance_km: 12.5,
        duration_minutes: 25,
        scheduled_time: null,
        pickup_address_short: 'Test Pickup',
        destination_address_short: 'Test Destination'
      };

      console.log('Trip data to insert:', JSON.stringify(testTripData, null, 2));

      const { data: tripResult, error: tripError } = await supabase
        .from('trips')
        .insert(testTripData)
        .select('*')
        .single();

      if (tripError) {
        console.error('❌ Trip creation failed:', tripError);
        
        if (tripError.code === '42501') {
          console.log('\n🔧 RLS Policy Issue Detected!');
          console.log('The trips table has Row-Level Security enabled but no policy allows this operation.');
          console.log('Please execute the SQL in: my-new-app/database/FIX_TRIPS_RLS_POLICY.sql');
          console.log('\nThis will create the necessary RLS policies for:');
          console.log('- Passengers to insert/view/update their own trips');
          console.log('- Drivers to view/update assigned trips');
          console.log('- Admins to manage all trips');
        }
        return;
      }

      console.log('✅ Trip created successfully!');
      console.log('Created trip:', JSON.stringify(tripResult, null, 2));

      // Step 5: Clean up - delete the test trip
      console.log('\n5. Cleaning up test data...');
      const { error: deleteError } = await supabase
        .from('trips')
        .delete()
        .eq('id', tripResult.id);

      if (deleteError) {
        console.warn('⚠️ Failed to delete test trip:', deleteError);
      } else {
        console.log('✅ Test trip cleaned up');
      }

      console.log('\n🎉 Authenticated trip creation test completed successfully!');
    } else {
      console.log(`✅ Authenticated as: ${user.email}`);
      
      // Continue with authenticated trip creation test...
      console.log('\n3. Testing authenticated trip creation...');
      // ... rest of authenticated test logic
    }

  } catch (error) {
    console.error('❌ Unexpected error during test:', error);
  }
}

// Additional function to test RLS policies specifically
async function testRLSPolicies() {
  console.log('\n🔒 Testing RLS Policies...\n');

  try {
    // Check if RLS is enabled on trips table
    const { data: rlsStatus, error: rlsError } = await supabase
      .rpc('check_rls_status', { table_name: 'trips' })
      .single();

    if (rlsError) {
      console.log('⚠️ Could not check RLS status (function may not exist)');
    } else {
      console.log('RLS Status:', rlsStatus);
    }

    // Try to query trips table policies (this might fail due to permissions)
    console.log('Checking if we can access trips table...');
    const { data: tripsCount, error: countError } = await supabase
      .from('trips')
      .select('id', { count: 'exact', head: true });

    if (countError) {
      console.error('❌ Cannot access trips table:', countError);
      if (countError.code === '42501') {
        console.log('🔧 This confirms RLS is blocking access. Execute FIX_TRIPS_RLS_POLICY.sql');
      }
    } else {
      console.log(`✅ Can access trips table. Found ${tripsCount} trips.`);
    }

  } catch (error) {
    console.error('❌ Error testing RLS policies:', error);
  }
}

// Run both tests
async function runAllTests() {
  await testAuthenticatedTripCreation();
  await testRLSPolicies();
}

runAllTests().catch(console.error);
