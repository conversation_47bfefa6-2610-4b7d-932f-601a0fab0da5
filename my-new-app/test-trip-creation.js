/**
 * Test Trip Creation - Debug Database Issues
 * Tests the trip booking functionality to identify database insertion problems
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client with correct credentials
const supabaseUrl = 'https://pcacyfyhxvzbjcouxzub.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjYWN5ZnloeHZ6Ympjb3V4enViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDE5MDgsImV4cCI6MjA2NzY3NzkwOH0.KXW5FFxjBo5crP_w5sXVK7xeH-fnhePo3FUs3MEIhZk';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testTripCreation() {
  console.log('🧪 Testing Trip Creation...\n');

  try {
    // Step 1: Check if trips table exists and get structure
    console.log('1. Checking trips table structure...');
    const { data: tableInfo, error: tableError } = await supabase
      .from('trips')
      .select('*')
      .limit(1);

    if (tableError) {
      console.error('❌ Error accessing trips table:', tableError);
      return;
    }
    console.log('✅ Trips table accessible');

    // Step 2: Get a valid passenger_id from profiles
    console.log('\n2. Finding valid passenger_id...');
    const { data: profiles, error: profileError } = await supabase
      .from('profiles')
      .select('id, full_name, user_type')
      .eq('user_type', 'passenger')
      .limit(1);

    if (profileError || !profiles || profiles.length === 0) {
      console.error('❌ No passenger profiles found:', profileError);
      return;
    }

    const passengerId = profiles[0].id;
    console.log(`✅ Found passenger: ${profiles[0].full_name} (${passengerId})`);

    // Step 3: Test trip data that matches the exact table structure
    console.log('\n3. Testing trip creation with correct data structure...');
    
    const testTripData = {
      passenger_id: passengerId,
      pickup_location: 'Test Pickup Location, Johannesburg',
      pickup_coordinates: '(28.0473,-26.2041)', // Correct POINT format
      destination_location: 'Test Destination, Sandton',
      destination_coordinates: '(28.0436,-26.1076)', // Correct POINT format
      ride_type: 'SheRide',
      status: 'requested',
      fare_amount: 45.50,
      distance_km: 12.5,
      duration_minutes: 25,
      scheduled_time: null,
      pickup_address_short: 'Test Pickup',
      destination_address_short: 'Test Destination'
    };

    console.log('Trip data to insert:', JSON.stringify(testTripData, null, 2));

    const { data: tripResult, error: tripError } = await supabase
      .from('trips')
      .insert(testTripData)
      .select('*')
      .single();

    if (tripError) {
      console.error('❌ Trip creation failed:', tripError);
      console.error('Error code:', tripError.code);
      console.error('Error message:', tripError.message);
      console.error('Error details:', tripError.details);
      
      // Provide specific guidance based on error code
      switch (tripError.code) {
        case '23503':
          console.log('\n🔍 Foreign key constraint violation - passenger_id may not exist in profiles table');
          break;
        case '42703':
          console.log('\n🔍 Column does not exist - check trips table structure');
          break;
        case '42P01':
          console.log('\n🔍 Table does not exist - trips table may not be created');
          break;
        case '22P02':
          console.log('\n🔍 Invalid input syntax - check POINT format or data types');
          break;
        default:
          console.log('\n🔍 Unknown error - check database logs');
      }
      return;
    }

    console.log('✅ Trip created successfully!');
    console.log('Created trip:', JSON.stringify(tripResult, null, 2));

    // Step 4: Clean up - delete the test trip
    console.log('\n4. Cleaning up test data...');
    const { error: deleteError } = await supabase
      .from('trips')
      .delete()
      .eq('id', tripResult.id);

    if (deleteError) {
      console.warn('⚠️ Failed to delete test trip:', deleteError);
    } else {
      console.log('✅ Test trip cleaned up');
    }

    console.log('\n🎉 Trip creation test completed successfully!');

  } catch (error) {
    console.error('❌ Unexpected error during test:', error);
  }
}

// Run the test
testTripCreation().catch(console.error);
