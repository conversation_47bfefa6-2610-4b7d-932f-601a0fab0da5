/**
 * Complete Trip Flow Test
 * Tests the entire trip booking flow after all fixes
 */

const { createClient } = require('@supabase/supabase-js');

// Initialize Supabase client
const supabaseUrl = 'https://pcacyfyhxvzbjcouxzub.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBjYWN5ZnloeHZ6Ympjb3V4enViIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIxMDE5MDgsImV4cCI6MjA2NzY3NzkwOH0.KXW5FFxjBo5crP_w5sXVK7xeH-fnhePo3FUs3MEIhZk';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testCompleteFlow() {
  console.log('🧪 Testing Complete Trip Booking Flow...\n');

  try {
    // Step 1: Test the get_nearby_drivers function (should work after POINT fix)
    console.log('1. Testing get_nearby_drivers function...');
    const { data: nearbyDrivers, error: driverError } = await supabase
      .rpc('get_nearby_drivers', {
        user_lat: -26.3019906,
        user_lng: 27.8769885,
        radius_km: 10.0
      });

    if (driverError) {
      console.error('❌ get_nearby_drivers function failed:', driverError);
      console.log('Please execute DEFINITIVE_POINT_FIX.sql in Supabase SQL Editor');
      return;
    }

    console.log(`✅ Found ${nearbyDrivers?.length || 0} drivers using database function`);
    if (nearbyDrivers && nearbyDrivers.length > 0) {
      console.log('Sample driver:', {
        name: nearbyDrivers[0].full_name,
        distance: nearbyDrivers[0].distance_km,
        location: nearbyDrivers[0].current_location
      });
    }

    // Step 2: Test trip creation with driver assignment
    console.log('\n2. Testing trip creation with driver assignment...');
    
    // Get passenger
    const { data: passengers } = await supabase
      .from('profiles')
      .select('id, full_name')
      .eq('user_type', 'passenger')
      .limit(1);

    if (!passengers || passengers.length === 0) {
      console.error('❌ No passenger found');
      return;
    }

    // Get driver
    const { data: drivers } = await supabase
      .from('drivers')
      .select('id, user_id')
      .eq('is_online', true)
      .eq('verification_status', 'approved')
      .limit(1);

    if (!drivers || drivers.length === 0) {
      console.error('❌ No online approved drivers found');
      return;
    }

    const testTripData = {
      passenger_id: passengers[0].id,
      driver_id: drivers[0].id,
      pickup_location: 'Test Pickup Location, Johannesburg',
      pickup_coordinates: '(28.0473,-26.2041)',
      destination_location: 'Test Destination, Sandton',
      destination_coordinates: '(28.0436,-26.1076)',
      ride_type: 'SheRide',
      status: 'accepted', // Auto-accepted status
      fare_amount: 45.50,
      distance_km: 12.5,
      duration_minutes: 25,
      pickup_address_short: 'Test Pickup',
      destination_address_short: 'Test Destination'
    };

    const { data: trip, error: tripError } = await supabase
      .from('trips')
      .insert(testTripData)
      .select('*')
      .single();

    if (tripError) {
      console.error('❌ Trip creation failed:', tripError);
      return;
    }

    console.log('✅ Trip created successfully with status:', trip.status);
    console.log('Trip details:', {
      id: trip.id,
      passenger_id: trip.passenger_id,
      driver_id: trip.driver_id,
      status: trip.status,
      fare_amount: trip.fare_amount
    });

    // Step 3: Test trip status flow
    console.log('\n3. Testing trip status transitions...');
    
    // Simulate driver accepting (already done with auto-accept)
    console.log('✅ Trip auto-accepted with driver assignment');
    
    // Test status update to in_progress
    const { error: updateError } = await supabase
      .from('trips')
      .update({ status: 'in_progress' })
      .eq('id', trip.id);

    if (updateError) {
      console.error('❌ Failed to update trip status:', updateError);
    } else {
      console.log('✅ Trip status updated to in_progress');
    }

    // Step 4: Clean up
    console.log('\n4. Cleaning up test data...');
    const { error: deleteError } = await supabase
      .from('trips')
      .delete()
      .eq('id', trip.id);

    if (deleteError) {
      console.warn('⚠️ Failed to delete test trip:', deleteError);
    } else {
      console.log('✅ Test trip cleaned up');
    }

    console.log('\n🎉 Complete trip flow test passed!');
    console.log('\n📋 Summary of fixes applied:');
    console.log('✅ Fixed POINT operator error in get_nearby_drivers function');
    console.log('✅ Fixed driver assignment by setting driver_id in trip creation');
    console.log('✅ Fixed trip status by auto-accepting trips (bypassing notification system)');
    console.log('✅ Fixed React key duplication by deduplicating driver arrays');
    console.log('\n🚀 Your trip booking should now work end-to-end!');

  } catch (error) {
    console.error('❌ Unexpected error during test:', error);
  }
}

// Run the test
testCompleteFlow().catch(console.error);
