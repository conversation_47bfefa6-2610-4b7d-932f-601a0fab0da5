import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,

  ActivityIndicator,
  Platform,
  Dimensions,
} from 'react-native';
import { Ionicons, MaterialIcons } from '@expo/vector-icons';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated from 'react-native-reanimated';
import { SearchResult } from '../services/geocodingService';
import { SmartSuggestion } from '../services/smartAddressService';

// SheMove color palette
const COLORS = {
  SOFT_PINK: '#FFF0FF',
  PINK: '#F9E6F7',
  ACCENT_PINK: '#E91E63',
  DEEP_PINK: '#C2185B',
  LIGHT_PINK: '#FCE4EC',
  WHITE: '#FFFFFF',
  DARK_TEXT: '#1A1A1A',
  MEDIUM_TEXT: '#666666',
  LIGHT_TEXT: '#999999',
  BORDER: '#F0F0F0',
};

interface SearchResultsProps {
  results: SearchResult[];
  smartSuggestions?: SmartSuggestion[];
  isLoading: boolean;
  onResultPress: (result: SearchResult) => void;
  onSuggestionPress?: (suggestion: SmartSuggestion) => void;
  visible: boolean;
  showHouseNumberHelp?: boolean;
  scrollHandler?: any; // Optional scroll handler for gesture coordination
}

const SearchResults: React.FC<SearchResultsProps> = ({
  results,
  isLoading,
  onResultPress,
  visible,
  scrollHandler,
}) => {
  if (!visible) {
    return null;
  }



  const getLocationIcon = (type: string): string => {
    switch (type) {
      case 'house':
      case 'building':
        return 'home';
      case 'restaurant':
      case 'cafe':
        return 'restaurant';
      case 'shop':
      case 'retail':
        return 'storefront';
      case 'hospital':
        return 'medical';
      case 'school':
      case 'university':
        return 'school';
      case 'bus_stop':
      case 'subway_station':
        return 'bus';
      case 'airport':
        return 'airplane';
      default:
        return 'location';
    }
  };

  const formatAddress = (result: SearchResult): { primary: string; secondary: string } => {
    // Google-style smart address formatting based on industry best practices
    const address = result.address;
    const displayParts = result.display_name.split(',').map(part => part.trim());

    // If we have structured address data, use smart formatting
    if (address) {
      return formatStructuredAddress(address, displayParts);
    }

    // Fallback to display_name parsing with Google-style logic
    return formatDisplayNameAddress(displayParts);
  };

  const formatStructuredAddress = (address: any, displayParts: string[]): { primary: string; secondary: string } => {
    let primary = '';
    let secondary = '';

    // Strategy 1: Specific Address (House + Street)
    if (address.house_number && address.road) {
      primary = `${address.house_number} ${address.road}`;
      secondary = buildSecondaryFromAddress(address);
    }
    // Strategy 2: Street/Road Only
    else if (address.road) {
      primary = address.road;
      secondary = buildSecondaryFromAddress(address);
    }
    // Strategy 3: Business/POI Name (from display_name first part)
    else if (displayParts.length > 0 && !isGenericLocation(displayParts[0])) {
      primary = displayParts[0];
      secondary = buildSecondaryFromAddress(address);
    }
    // Strategy 4: Neighborhood/Suburb
    else if (address.neighbourhood || address.suburb) {
      primary = address.neighbourhood || address.suburb;
      secondary = buildSecondaryFromAddress(address, true); // Skip neighborhood in secondary
    }
    // Strategy 5: City/Town
    else if (address.city || address.town) {
      primary = address.city || address.town;
      secondary = buildRegionalSecondary(address);
    }
    // Strategy 6: Fallback to display name
    else {
      return formatDisplayNameAddress(displayParts);
    }

    return { primary, secondary };
  };

  const buildSecondaryFromAddress = (address: any, skipNeighborhood = false): string => {
    const parts: string[] = [];

    // Add neighborhood/suburb (unless we're skipping it)
    if (!skipNeighborhood) {
      if (address.neighbourhood) {
        parts.push(address.neighbourhood);
      } else if (address.suburb) {
        parts.push(address.suburb);
      }
    }

    // Add city/town
    if (address.city) {
      parts.push(address.city);
    } else if (address.town) {
      parts.push(address.town);
    }

    // For South African users, only add province/state if it's not obvious
    // Skip "South Africa" for local searches to reduce clutter
    if (address.state && !isObviousLocation(address.state)) {
      parts.push(address.state);
    }

    // Only add country if it's not South Africa (for local users)
    if (address.country && address.country.toLowerCase() !== 'south africa') {
      parts.push(address.country);
    }

    return parts.slice(0, 2).join(', '); // Limit to 2 parts for clean display
  };

  const buildRegionalSecondary = (address: any): string => {
    const parts: string[] = [];

    // For cities, show state/province and country (if not SA)
    if (address.state) {
      parts.push(address.state);
    }

    if (address.country && address.country.toLowerCase() !== 'south africa') {
      parts.push(address.country);
    }

    return parts.join(', ');
  };

  const formatDisplayNameAddress = (displayParts: string[]): { primary: string; secondary: string } => {
    if (displayParts.length === 0) {
      return { primary: 'Unknown location', secondary: '' };
    }

    if (displayParts.length === 1) {
      return { primary: displayParts[0], secondary: '' };
    }

    // Google-style: First part as primary, next 1-2 parts as secondary
    const primary = displayParts[0];

    // Enhanced filtering for cleaner secondary text
    const secondaryParts = displayParts.slice(1, 4).filter(part => {
      const lowerPart = part.toLowerCase();
      return !lowerPart.includes('south africa') && // Filter out country
             !isObviousLocation(part); // Filter out administrative divisions
    });

    return {
      primary,
      secondary: secondaryParts.slice(0, 2).join(', ') // Limit to 2 parts max
    };
  };

  const isGenericLocation = (text: string): boolean => {
    const generic = ['city', 'town', 'village', 'municipality', 'ward', 'district'];
    return generic.some(term => text.toLowerCase().includes(term));
  };

  const isObviousLocation = (text: string): boolean => {
    const lowerText = text.toLowerCase();

    // For South African users, these are obvious and can be hidden
    const obvious = [
      // Provinces
      'gauteng', 'western cape', 'kwazulu-natal', 'eastern cape',
      'free state', 'limpopo', 'mpumalanga', 'north west', 'northern cape',
      // Administrative divisions
      'ward', 'municipality', 'metropolitan municipality',
      'local municipality', 'district municipality',
      // Generic terms
      'city of', 'greater', 'metro'
    ];

    return obvious.some(term => lowerText.includes(term));
  };

  const renderSearchResult = ({ item }: { item: SearchResult }) => {
    const { primary, secondary } = formatAddress(item);
    const iconName = getLocationIcon(item.type);

    return (
      <TouchableOpacity
        style={styles.resultItem}
        onPress={() => onResultPress(item)}
        activeOpacity={0.7}
      >
        <View style={styles.resultIcon}>
          <Ionicons name={iconName as any} size={20} color={COLORS.ACCENT_PINK} />
        </View>
        <View style={styles.resultContent}>
          <Text style={styles.resultPrimary} numberOfLines={1}>
            {primary}
          </Text>
          {secondary ? (
            <Text style={styles.resultSecondary} numberOfLines={1}>
              {secondary}
            </Text>
          ) : null}
        </View>
        <View style={styles.resultArrow}>
          <Ionicons name="chevron-forward" size={16} color={COLORS.LIGHT_TEXT} />
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <View style={styles.container}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="small" color={COLORS.ACCENT_PINK} />
          <Text style={styles.loadingText}>Searching...</Text>
        </View>
      ) : results.length > 0 ? (
        {/* Use regular View instead of FlatList to avoid VirtualizedList nesting */}
        <View style={styles.resultsList}>
          <View style={styles.resultsContent}>
            {results.map((item, index) => (
              <View key={item.place_id}>
                {renderSearchResult({ item, index })}
              </View>
            ))}
          </View>
        </View>
      ) : (
        <View style={styles.noResultsContainer}>
          <Ionicons name="search" size={24} color={COLORS.LIGHT_TEXT} />
          <Text style={styles.noResultsText}>No locations found</Text>
          <Text style={styles.noResultsSubtext}>Try a different search term</Text>
        </View>
      )}
    </View>
  );
};

const { height } = Dimensions.get('window');

const styles = StyleSheet.create({
  container: {
    backgroundColor: COLORS.WHITE,
    borderRadius: 12,
    marginTop: 8,
    minHeight: 150, // Increased minimum height for better visibility
    maxHeight: height * 0.5, // Reasonable max height for mobile
    shadowColor: '#000',
    shadowOpacity: 0.15, // Increased shadow for better visibility
    shadowRadius: 12,
    shadowOffset: { width: 0, height: 4 },
    elevation: 8,
    zIndex: 1000, // Very high z-index to ensure visibility
    position: 'relative',
    // Force visibility
    opacity: 1,
    overflow: 'visible',
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  loadingText: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
    marginLeft: 8,
  },
  resultsList: {
    // Remove maxHeight to allow full scrolling of all results
    flex: 1,
  },
  resultsContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'ios' ? 30 : 20, // Platform-specific spacing
  },

  resultItem: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: COLORS.BORDER,
  },
  resultIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.LIGHT_PINK,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  resultContent: {
    flex: 1,
  },
  resultPrimary: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.DARK_TEXT,
    marginBottom: 2,
  },
  resultSecondary: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.MEDIUM_TEXT,
  },
  resultArrow: {
    marginLeft: 8,
  },
  noResultsContainer: {
    alignItems: 'center',
    padding: 32,
  },
  noResultsText: {
    fontSize: 16,
    fontFamily: 'Urbanist-Bold',
    fontWeight: '600',
    color: COLORS.MEDIUM_TEXT,
    marginTop: 8,
    marginBottom: 4,
  },
  noResultsSubtext: {
    fontSize: 14,
    fontFamily: 'Urbanist-Bold',
    color: COLORS.LIGHT_TEXT,
  },
});

export default SearchResults;
