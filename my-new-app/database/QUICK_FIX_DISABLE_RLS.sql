-- =====================================================
-- QUICK FIX: DISABLE RLS ON TRIPS TABLE
-- =====================================================
-- This is a temporary fix to allow trip creation while we debug
-- For production, use the proper RLS policies in FIX_TRIPS_RLS_POLICY.sql

-- Disable RLS on trips table temporarily
ALTER TABLE trips DISABLE ROW LEVEL SECURITY;

-- Grant permissions to allow trip operations
GRANT SELECT, INSERT, UPDATE, DELETE ON trips TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON trips TO anon;

-- Verify RLS is disabled
SELECT schemaname, tablename, rowsecurity, forcerowsecurity 
FROM pg_tables 
WHERE tablename = 'trips';

SELECT 'RLS disabled on trips table - trip creation should work now!' as status;
