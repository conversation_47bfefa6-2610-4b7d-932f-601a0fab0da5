-- =====================================================
-- FIX TRIPS TABLE RLS POLICY
-- =====================================================
-- Error: "new row violates row-level security policy for table trips"
-- Solution: Create proper RLS policies for trips table

-- Check current RLS status
SELECT schemaname, tablename, rowsecurity, forcerowsecurity 
FROM pg_tables 
WHERE tablename = 'trips';

-- Check existing policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'trips';

-- Enable RLS on trips table (if not already enabled)
ALTER TABLE trips ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to recreate them properly
DROP POLICY IF EXISTS "Users can view their own trips" ON trips;
DROP POLICY IF EXISTS "Users can insert their own trips" ON trips;
DROP POLICY IF EXISTS "Users can update their own trips" ON trips;
DROP POLICY IF EXISTS "Drivers can view assigned trips" ON trips;
DROP POLICY IF EXISTS "Drivers can update assigned trips" ON trips;

-- Create comprehensive RLS policies for trips table

-- 1. Allow passengers to insert their own trips
CREATE POLICY "Passengers can insert their own trips" ON trips
  FOR INSERT 
  TO authenticated
  WITH CHECK (
    auth.uid() = passenger_id
  );

-- 2. Allow passengers to view their own trips
CREATE POLICY "Passengers can view their own trips" ON trips
  FOR SELECT 
  TO authenticated
  USING (
    auth.uid() = passenger_id
  );

-- 3. Allow passengers to update their own trips (for cancellation, etc.)
CREATE POLICY "Passengers can update their own trips" ON trips
  FOR UPDATE 
  TO authenticated
  USING (auth.uid() = passenger_id)
  WITH CHECK (auth.uid() = passenger_id);

-- 4. Allow drivers to view trips assigned to them
CREATE POLICY "Drivers can view assigned trips" ON trips
  FOR SELECT 
  TO authenticated
  USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- 5. Allow drivers to update trips assigned to them
CREATE POLICY "Drivers can update assigned trips" ON trips
  FOR UPDATE 
  TO authenticated
  USING (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  )
  WITH CHECK (
    driver_id IN (
      SELECT id FROM drivers WHERE user_id = auth.uid()
    )
  );

-- 6. Allow admin users to manage all trips
CREATE POLICY "Admins can manage all trips" ON trips
  FOR ALL 
  TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND user_type = 'admin'
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() 
      AND user_type = 'admin'
    )
  );

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON trips TO authenticated;
GRANT SELECT, INSERT, UPDATE ON trips TO anon;

-- Verify policies are created
SELECT 'RLS policies created successfully!' as status;

-- Show all policies for trips table
SELECT policyname, permissive, roles, cmd, qual, with_check
FROM pg_policies 
WHERE tablename = 'trips'
ORDER BY policyname;
