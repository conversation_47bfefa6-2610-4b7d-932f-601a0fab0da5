-- =====================================================
-- BASIC POINT FIX (No PostGIS Required)
-- =====================================================
-- Error: "function st_distance_sphere(geometry, geometry) does not exist"
-- Solution: Use basic PostgreSQL POINT operators and Haversine formula
-- This works with standard PostgreSQL without requiring PostGIS

-- Drop existing function to avoid conflicts
DROP FUNCTION IF EXISTS get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION);

-- Create the corrected function using basic PostgreSQL POINT operations
CREATE OR REPLACE FUNCTION get_nearby_drivers(
  user_lat DOUBLE PRECISION,
  user_lng DOUBLE PRECISION,
  radius_km DOUBLE PRECISION DEFAULT 5.0
)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  full_name TEXT,
  current_location JSONB,
  vehicle_make TEXT,
  vehicle_model TEXT,
  vehicle_color TEXT,
  vehicle_type TEXT,
  is_online BOOLEAN,
  rating NUMERIC,
  distance_km DOUBLE PRECISION
) 
LANGUAGE plpgsql
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    d.id,
    d.user_id,
    COALESCE(p.full_name, 'Driver') as full_name,
    -- Convert POINT to JSONB using basic PostgreSQL operators
    CASE 
      WHEN d.current_location IS NULL THEN NULL
      ELSE jsonb_build_object(
        'lat', (d.current_location)[1],  -- Y coordinate (latitude)
        'lng', (d.current_location)[0]   -- X coordinate (longitude)
      )
    END as current_location,
    d.vehicle_make,
    d.vehicle_model,
    d.vehicle_color,
    COALESCE(d.vehicle_type::TEXT, 'SheRide') as vehicle_type,
    d.is_online,
    COALESCE(d.rating, 4.5) as rating,
    -- Haversine formula using basic PostgreSQL functions
    CASE 
      WHEN d.current_location IS NULL THEN 999999.0
      ELSE
        -- Haversine distance calculation (accurate for short distances)
        (
          6371 * acos(
            GREATEST(-1, LEAST(1,
              cos(radians(user_lat)) * 
              cos(radians((d.current_location)[1])) * 
              cos(radians((d.current_location)[0]) - radians(user_lng)) + 
              sin(radians(user_lat)) * 
              sin(radians((d.current_location)[1]))
            ))
          )
        )
    END as distance_km
  FROM drivers d
  LEFT JOIN profiles p ON d.user_id = p.id
  WHERE 
    d.is_online = true
    AND d.verification_status = 'approved'
    AND d.current_location IS NOT NULL
    -- Simple bounding box filter using POINT coordinates
    AND (d.current_location)[1] BETWEEN (user_lat - (radius_km / 111.0)) AND (user_lat + (radius_km / 111.0))
    AND (d.current_location)[0] BETWEEN (user_lng - (radius_km / (111.0 * cos(radians(user_lat))))) AND (user_lng + (radius_km / (111.0 * cos(radians(user_lat)))))
  ORDER BY distance_km ASC
  LIMIT 20;
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO authenticated;
GRANT EXECUTE ON FUNCTION get_nearby_drivers(DOUBLE PRECISION, DOUBLE PRECISION, DOUBLE PRECISION) TO anon;

-- Test the function
SELECT 'Basic POINT function created successfully!' as status;

-- Test with your actual driver coordinates
SELECT 
  id,
  full_name,
  current_location,
  vehicle_make,
  vehicle_model,
  is_online,
  distance_km
FROM get_nearby_drivers(-26.3019906, 27.8769885, 10.0);

SELECT 'Test completed - function should work without PostGIS!' as final_status;
